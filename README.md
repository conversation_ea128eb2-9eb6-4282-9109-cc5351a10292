# 维度评估框架使用指南

## 概述

本项目提供了一个支持多维度的可扩展评估体系，包含以下核心维度：

- **完整性维度** - 评估内容的完整程度
- **正确性维度** - 评估内容的准确性和可靠性  
- **难度分级维度** - 评估内容的复杂程度和难度等级

## 核心特性

### ✨ 多维度评估体系
- **完整性维度**: 必填字段完整性、内容结构完整性、测试步骤完整性、代码片段完整性
- **正确性维度**: 格式正确性、逻辑一致性、数据有效性
- **难度分级维度**: 技术复杂度、业务复杂度、测试覆盖度

### 🔧 可扩展架构
- 支持动态指标注册和配置
- 每个维度的指标可独立启用/禁用
- 支持自定义指标权重
- 提供清晰的接口来定义新指标

### 📊 灵活评估模式
- 可独立评估单个维度
- 可组合使用生成综合评分
- 提供详细的评估结果和改进建议

## 快速开始

### 1. 基本使用

```python
from ft_corpus_evaluator.core.evaluator_manager import EvaluatorManager
from ft_corpus_evaluator.parsers.corpus_parser import CorpusParser

# 创建维度评估管理器
manager = EvaluatorManager()

# 解析语料库文件
parser = CorpusParser()
corpus = parser.parse_file("your_test_case.md")

# 执行综合评估
report = manager.evaluate_corpus(corpus)
print(f"综合评分: {report.overall_score:.2f}")
```

### 2. 维度特定评估

```python
from ft_corpus_evaluator.models.evaluation_models import EvaluationDimension

# 评估特定维度
completeness_result = manager.evaluate_by_dimension(corpus, EvaluationDimension.COMPLETENESS)
correctness_result = manager.evaluate_by_dimension(corpus, EvaluationDimension.CORRECTNESS)
difficulty_result = manager.evaluate_by_dimension(corpus, EvaluationDimension.DIFFICULTY)

print(f"完整性评分: {completeness_result.overall_score:.2f}")
print(f"正确性评分: {correctness_result.overall_score:.2f}")
print(f"难度分级评分: {difficulty_result.overall_score:.2f}")
```

### 3. 指标管理

```python
# 查看当前指标状态
metrics_status = manager.get_dimension_metrics_status()

# 禁用特定指标
manager.disable_dimension_metric(EvaluationDimension.COMPLETENESS, "code_snippets_completeness")

# 启用特定指标
manager.enable_dimension_metric(EvaluationDimension.CORRECTNESS, "format_correctness")
```

### 4. 批量处理

```python
from ft_corpus_evaluator.core.batch_processor import BatchProcessor

processor = BatchProcessor(manager)

# 批量处理目录
result = processor.process_directory(
    corpus_dir=Path("corpus"),
    file_pattern="*.md",
    output_dir=Path("evaluation_results")
)

# 按维度批量处理
dimension_result = processor.process_directory_by_dimension(
    corpus_dir=Path("corpus"),
    dimension=EvaluationDimension.COMPLETENESS,
    output_dir=Path("dimension_results")
)
```

## 配置管理

### 默认配置

框架使用 `ft_corpus_evaluator/config/evaluator_config.json` 作为默认配置文件，包含：

- 各维度的启用状态和权重
- 每个指标的配置参数
- 评估阈值和规则

### 自定义配置

```python
# 使用自定义配置文件
manager = EvaluatorManager(dimension_config_path="custom_config.json")
```

配置文件示例：
```json
{
  "dimensions": {
    "completeness": {
      "enabled": true,
      "weight": 1.0,
      "metrics": {
        "required_fields": {
          "enabled": true,
          "weight": 1.0
        }
      }
    }
  }
}
```

## 维度详解

### 完整性维度 (Completeness)

评估内容的完整程度，包含以下指标：

- **required_fields**: 检查必填字段是否完整
- **content_structure**: 验证内容结构的完整性
- **test_steps_completeness**: 评估测试步骤的完整性
- **code_snippets_completeness**: 检查代码片段的完整性

### 正确性维度 (Correctness)

评估内容的准确性和可靠性，包含以下指标：

- **format_correctness**: 验证格式的正确性
- **logical_consistency**: 检查逻辑一致性
- **data_validity**: 验证数据的有效性

### 难度分级维度 (Difficulty)

评估内容的复杂程度和难度等级，包含以下指标：

- **technical_complexity**: 评估技术复杂度
- **business_complexity**: 评估业务复杂度
- **test_coverage_complexity**: 评估测试覆盖度复杂性

## 扩展指南

### 添加新指标

1. 继承 `BaseMetric` 类：

```python
from ft_corpus_evaluator.evaluators.dimension_framework import BaseMetric

class CustomMetric(BaseMetric):
    def __init__(self, weight: float = 1.0, enabled: bool = True):
        super().__init__("custom_metric", weight, enabled)
    
    def evaluate(self, corpus: FTCorpus) -> MetricResult:
        # 实现评估逻辑
        score = self._calculate_score(corpus)
        return self.create_result(
            score=score,
            level=EvaluationLevel.INFO,
            message="Custom metric evaluation",
            dimension=EvaluationDimension.COMPLETENESS
        )
```

2. 注册到相应维度：

```python
dimension = CompletenessDimension()
dimension.register_metric(CustomMetric())
```

### 添加新维度

1. 继承 `BaseDimension` 类：

```python
from ft_corpus_evaluator.evaluators.dimension_framework import BaseDimension

class CustomDimension(BaseDimension):
    def __init__(self):
        super().__init__(EvaluationDimension.CUSTOM)
        # 注册指标
        self.register_metric(CustomMetric())
```

2. 注册到维度管理器：

```python
manager = DimensionManager()
manager.register_dimension(CustomDimension())
```

## 运行示例

### 简单演示
```bash
python examples/simple_dimension_demo.py
```

### 完整示例
```bash
python examples/dimension_usage_example.py
```

### 运行测试
```bash
python tests/test_dimension_evaluator.py
```

## 输出格式

### 评估结果结构

```json
{
  "corpus_id": "RAN-1234567",
  "file_path": "test_case.md",
  "overall_score": 78.5,
  "evaluation_results": [
    {
      "evaluator_name": "dimension_evaluator_completeness",
      "dimension": "completeness",
      "score": 85.0,
      "level": "info",
      "message": "完整性维度评分良好",
      "details": {
        "metric_results": [...],
        "enabled_metrics": [...]
      },
      "suggestions": [...]
    }
  ]
}
```

## 性能优化

- 支持并发处理多个文件
- 可配置的工作线程数量
- 指标级别的启用/禁用控制
- 缓存机制减少重复计算

## 架构特点

- 专注于多维度评估体系
- 简洁清晰的API设计
- 高度可扩展的架构

## 故障排除

### 常见问题

1. **配置文件加载失败**
   - 检查配置文件路径和格式
   - 使用默认配置作为参考

2. **指标评估异常**
   - 检查输入数据格式
   - 查看详细错误信息

3. **性能问题**
   - 调整并发线程数
   - 禁用不必要的指标

### 调试模式

```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

## 贡献指南

欢迎贡献新的指标和维度！请遵循以下步骤：

1. Fork 项目
2. 创建特性分支
3. 实现新功能并添加测试
4. 提交 Pull Request

## 许可证

本项目采用 MIT 许可证。
